import pygame
import sys
import random # Added for particle effects and wobble

# Initialize Pygame
pygame.init()
pygame.mixer.init() # Initialize the mixer

# Screen dimensions
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Ping Pong Game")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0) # Color for collision flash

RED = (255, 0, 0) # Color for collision flash
ORANGE = (255, 165, 0) # Color for particles

# Paddle dimensions
PADDLE_WIDTH, PADDLE_HEIGHT = 10, 100

# Ball dimensions
BALL_SIZE = 20

# Paddle positions
left_paddle = pygame.Rect(30, HEIGHT // 2 - PADDLE_HEIGHT // 2, PADDLE_WIDTH, PADDLE_HEIGHT)
right_paddle = pygame.Rect(WIDTH - 30 - PADDLE_WIDTH, HEIGHT // 2 - PADDLE_HEIGHT // 2, PADD<PERSON>_WIDTH, PADDLE_HEIGHT)

# Ball position and speed
ball = pygame.Rect(WIDTH // 2 - BALL_SIZE // 2, HEIGHT // 2 - BALL_SIZE // 2, BALL_SIZE, BALL_SIZE)
INITIAL_BALL_SPEED_X = 7
INITIAL_BALL_SPEED_Y = 7
ball_speed_x = INITIAL_BALL_SPEED_X
ball_speed_y = INITIAL_BALL_SPEED_Y
SPEED_INCREASE = 0.5 # How much speed increases per paddle hit
MAX_SPEED = 15 # Maximum horizontal speed

# Paddle speed
paddle_speed = 10

# Scores
left_score = 0
right_score = 0

# Font for displaying score
font = pygame.font.Font(None, 74)

# Clock
clock = pygame.time.Clock()

# Collision animation timer
ball_collision_timer = 0
COLLISION_FLASH_DURATION = 5 # Frames the ball flashes red

COLLISION_FLASH_DURATION = 5 # Frames the ball flashes red

# --- Animation Variables ---
particles = [] # List to store active particles
PARTICLE_COUNT = 10
PARTICLE_SPEED = 3
PARTICLE_LIFESPAN = 15 # Frames

wall_wobble_timer = 0
WALL_WOBBLE_DURATION = 10 # Frames
WALL_WOBBLE_AMOUNT = 5 # Max pixels displacement

# Simple Particle Class
class Particle:
    def __init__(self, x, y, vx, vy, lifespan):
        self.x = x
        self.y = y
        self.vx = vx
        self.vy = vy
        self.lifespan = lifespan
        self.size = random.randint(2, 5) # Random size for particles

    def update(self):
        self.x += self.vx
        self.y += self.vy
        self.lifespan -= 1

    def draw(self, surface):
        # Fade effect (optional, simple size decrease here)
        # current_size = int(self.size * (self.lifespan / PARTICLE_LIFESPAN))
        # if current_size > 0:
        #     pygame.draw.rect(surface, ORANGE, (self.x, self.y, current_size, current_size))
        pygame.draw.rect(surface, ORANGE, (self.x, self.y, self.size, self.size))


# Load sounds (place sound files in the same directory as the script)
try:
    paddle_hit_sound = pygame.mixer.Sound("paddle_hit.wav")
    wall_bounce_sound = pygame.mixer.Sound("wall_bounce.wav")
    score_sound = pygame.mixer.Sound("score_point.wav")
    sound_files_loaded = True
except Exception as e: # Catch broader exceptions including FileNotFoundError
    print(f"Warning: Could not load sound files (paddle_hit.wav, wall_bounce.wav, score_point.wav). Error: {e}")
    print("Game will run without sound effects.")
    # Create dummy sound objects if loading fails to avoid errors later
    paddle_hit_sound = pygame.mixer.Sound(buffer=b'') # Dummy sound
    wall_bounce_sound = pygame.mixer.Sound(buffer=b'') # Dummy sound
    score_sound = pygame.mixer.Sound(buffer=b'') # Dummy sound
    sound_files_loaded = False # Flag to indicate sounds are missing


# Function to reset the ball
def reset_ball():
    ball.x, ball.y = WIDTH // 2 - BALL_SIZE // 2, HEIGHT // 2 - BALL_SIZE // 2
    # Reset speed to initial magnitude but reverse direction from previous
    new_speed_x = INITIAL_BALL_SPEED_X * (-1 if ball_speed_x > 0 else 1)
    new_speed_y = INITIAL_BALL_SPEED_Y * (-1 if ball_speed_y > 0 else 1) # Or keep random y? Let's keep it simple.
    # Randomize y direction slightly on reset? Could add import random for that.
    # import random
    # new_speed_y = INITIAL_BALL_SPEED_Y * random.choice([-1, 1])
    return new_speed_x, new_speed_y

# Game loop
running = True
while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False # Exit loop cleanly

    # --- Update Animation Timers ---
    if ball_collision_timer > 0:
        ball_collision_timer -= 1
    if wall_wobble_timer > 0:
        wall_wobble_timer -= 1

    # --- Update Particles ---
    particles = [p for p in particles if p.lifespan > 0] # Remove dead particles
    for p in particles:
        p.update()

    # Paddle movement
    keys = pygame.key.get_pressed()
    if keys[pygame.K_w] and left_paddle.top > 0:
        left_paddle.y -= paddle_speed
    if keys[pygame.K_s] and left_paddle.bottom < HEIGHT:
        left_paddle.y += paddle_speed
    if keys[pygame.K_UP] and right_paddle.top > 0:
        right_paddle.y -= paddle_speed
    if keys[pygame.K_DOWN] and right_paddle.bottom < HEIGHT:
        right_paddle.y += paddle_speed

    # Ball movement
    ball.x += ball_speed_x
    ball.y += ball_speed_y

    # Ball collision with top and bottom
    if ball.top <= 0 or ball.bottom >= HEIGHT:
        ball_speed_y *= -1
        if sound_files_loaded: wall_bounce_sound.play() # Play wall bounce sound
        ball_collision_timer = COLLISION_FLASH_DURATION # Start flash timer
        wall_wobble_timer = WALL_WOBBLE_DURATION # Start wall wobble

    # Ball collision with paddles
    collided_paddle = None
    if ball.colliderect(left_paddle):
        collided_paddle = left_paddle
    elif ball.colliderect(right_paddle):
        collided_paddle = right_paddle

    if collided_paddle:
        ball_speed_x *= -1
        if sound_files_loaded: paddle_hit_sound.play() # Play paddle hit sound
        ball_collision_timer = COLLISION_FLASH_DURATION # Start flash timer

        # --- Create Particles on Paddle Hit ---
        collision_point_y = ball.centery
        # Determine collision side for particle direction
        if collided_paddle == left_paddle:
            collision_point_x = left_paddle.right
            base_angle = 0 # Radians (right)
        else: # Right paddle
            collision_point_x = right_paddle.left
            base_angle = 3.14159 # Radians (left)

        for _ in range(PARTICLE_COUNT):
            angle_offset = random.uniform(-0.8, 0.8) # Spread particles (approx +/- 45 degrees)
            angle = base_angle + angle_offset
            vx = PARTICLE_SPEED * random.uniform(0.5, 1.5) * pygame.math.Vector2(1, 0).rotate(angle * 180 / 3.14159).x # Use pygame vectors for rotation
            vy = PARTICLE_SPEED * random.uniform(0.5, 1.5) * pygame.math.Vector2(1, 0).rotate(angle * 180 / 3.14159).y
            particles.append(Particle(collision_point_x, collision_point_y, vx, vy, PARTICLE_LIFESPAN))
        # --- End Particle Creation ---

        # Increase speed
        if abs(ball_speed_x) < MAX_SPEED:
            ball_speed_x += SPEED_INCREASE if ball_speed_x > 0 else -SPEED_INCREASE
            # Optionally increase y speed too
            # ball_speed_y += SPEED_INCREASE / 2 if ball_speed_y > 0 else -SPEED_INCREASE / 2


    # Ball out of bounds
    if ball.left <= 0:
        right_score += 1
        ball_speed_x, ball_speed_y = reset_ball()
        if sound_files_loaded: score_sound.play() # Play score sound
    if ball.right >= WIDTH:
        left_score += 1
        ball_speed_x, ball_speed_y = reset_ball()
        if sound_files_loaded: score_sound.play() # Play score sound

    # Drawing
    screen.fill(BLACK)
    pygame.draw.rect(screen, WHITE, left_paddle)
    pygame.draw.rect(screen, WHITE, right_paddle)
    # Determine ball color based on collision timer
    current_ball_color = RED if ball_collision_timer > 0 else WHITE
    pygame.draw.ellipse(screen, current_ball_color, ball)

    # --- Draw Center Line (with wobble) ---
    center_x = WIDTH // 2
    if wall_wobble_timer > 0:
        # Simple wobble: random horizontal offset
        wobble_offset = random.randint(-WALL_WOBBLE_AMOUNT, WALL_WOBBLE_AMOUNT)
        # Could also do a sine wave:
        # wobble_offset = int(WALL_WOBBLE_AMOUNT * (wall_wobble_timer / WALL_WOBBLE_DURATION) * math.sin(pygame.time.get_ticks() * 0.1))
        center_x += wobble_offset
    pygame.draw.aaline(screen, WHITE, (center_x, 0), (center_x, HEIGHT))
    # --- End Center Line ---

    # --- Draw Particles ---
    for p in particles:
        p.draw(screen)
    # --- End Particles ---

    # Display scores
    left_score_text = font.render(str(left_score), True, WHITE)
    right_score_text = font.render(str(right_score), True, WHITE)
    screen.blit(left_score_text, (WIDTH // 4, 20))
    screen.blit(right_score_text, (3 * WIDTH // 4 - right_score_text.get_width(), 20))

    # Update the display
    pygame.display.flip()

    # Cap the frame rate
    clock.tick(60)

    if left_score >= 20 or right_score >= 20:
        running = False # Exit loop cleanly

# Quit Pygame outside the loop
pygame.quit()
sys.exit()

