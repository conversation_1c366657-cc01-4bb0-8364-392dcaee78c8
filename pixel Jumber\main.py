import pygame
import asyncio
import platform
import random

# Initialize Pygame
pygame.init()

# Screen settings
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Pixel Jumper")
FPS = 60
clock = pygame.time.Clock()

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)


# Player class
class Player(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((40, 40))
        self.image.fill(BLUE)
        self.rect = self.image.get_rect(topleft=(x, y))
        self.vel_y = 0
        self.jump_power = -15
        self.gravity = 0.8
        self.on_ground = False

    def update(self, platforms):
        self.vel_y += self.gravity
        self.rect.y += self.vel_y

        self.on_ground = False
        for platform in platforms:
            if self.rect.colliderect(platform.rect) and self.vel_y > 0:
                self.rect.bottom = platform.rect.top
                self.vel_y = 0
                self.on_ground = True

        if self.rect.bottom > HEIGHT:
            self.rect.bottom = HEIGHT
            self.vel_y = 0
            self.on_ground = True
        if self.rect.left < 0:
            self.rect.left = 0
        if self.rect.right > WIDTH:
            self.rect.right = WIDTH

    def jump(self):
        if self.on_ground:
            self.vel_y = self.jump_power


# Platform class
class Platform(pygame.sprite.Sprite):
    def __init__(self, x, y, width):
        super().__init__()
        self.image = pygame.Surface((width, 20))
        self.image.fill(GREEN)
        self.rect = self.image.get_rect(topleft=(x, y))


# Coin class
class Coin(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((20, 20))
        self.image.fill(YELLOW)
        self.rect = self.image.get_rect(center=(x, y))


# Game setup
def setup():
    global all_sprites, platforms, coins, player, score
    all_sprites = pygame.sprite.Group()
    platforms = pygame.sprite.Group()
    coins = pygame.sprite.Group()
    score = 0

    player = Player(100, HEIGHT - 100)
    all_sprites.add(player)

    for i in range(5):
        x = random.randint(0, WIDTH - 100)
        y = HEIGHT - i * 100 - 50
        platform = Platform(x, y, 100)
        all_sprites.add(platform)
        platforms.add(platform)

    for i in range(3):
        x = random.randint(50, WIDTH - 50)
        y = HEIGHT - i * 150 - 100
        coin = Coin(x, y)
        all_sprites.add(coin)
        coins.add(coin)


# Update game state
def update_loop():
    global score
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            pygame.quit()
            exit()

    keys = pygame.key.get_pressed()
    if keys[pygame.K_LEFT]:
        player.rect.x -= 5
    if keys[pygame.K_RIGHT]:
        player.rect.x += 5
    if keys[pygame.K_SPACE]:
        player.jump()

    player.update(platforms)

    # Update only non-player sprites
    for sprite in all_sprites:
        if sprite != player:
            sprite.update()

    collected = pygame.sprite.spritecollide(player, coins, True)
    score += len(collected) * 10

    screen.fill(BLACK)
    all_sprites.draw(screen)
    font = pygame.font.SysFont(None, 36)
    score_text = font.render(f"Score: {score}", True, WHITE)
    screen.blit(score_text, (10, 10))
    pygame.display.flip()


# Main game loop for Pyodide and desktop
async def main():
    setup()
    while True:
        update_loop()
        await asyncio.sleep(1.0 / FPS)


# Run game
if platform.system() == "Emscripten":
    asyncio.ensure_future(main())
else:
    if __name__ == "__main__":
        asyncio.run(main())
