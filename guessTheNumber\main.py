import streamlit as st
import random

# Initialize session state
if 'matcher' not in st.session_state:
    st.session_state.matcher = random.choice(list(range(1, 11)))
    st.session_state.attempts = 3
    st.session_state.message = ""

st.title("🎯 Number Guessing Game")
st.subheader("Guess a number between 1 and 10")

guess = st.number_input("Enter your guess:", min_value=1, max_value=10, step=1)

if st.button("Submit Guess"):
    if st.session_state.attempts > 0:
        if guess == st.session_state.matcher:
            st.success("🎉 You won!")
            st.session_state.attempts = 0
        else:
            st.session_state.attempts -= 1
            if st.session_state.attempts > 0:
                st.warning(f"❌ Nope! {st.session_state.attempts} chances left.")
            else:
                st.error(f"😢 Game over! The correct number was {st.session_state.matcher}.")

if st.button("Restart Game"):
    st.session_state.matcher = random.choice(list(range(1, 11)))
    st.session_state.attempts = 3
    st.session_state.message = ""
    st.rerun()

